from ultralytics import YOLO
import os


def train_yolo():
    # 初始化关键点检测模型
    model = YOLO("yolo11n-pose.pt")  # 加载关键点检测预训练模型

    results = model.train(data="data.yaml", epochs=40, imgsz=320,device=0,batch=10,workers=2)

    # 保存训练结果
    model.save('11n_best.pt')  # 保存最佳模型
    model.save('11n_last.pt')  # 保存最后一轮模型


if __name__ == '__main__':
    import torch

    print(torch.version.cuda)  # 显示 CUDA 版本
    print(torch.cuda.is_available())  # 检查 CUDA 是否可用
    print(torch.cuda.get_device_name(0))  # 显示 GPU 名称
    train_yolo()
