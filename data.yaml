# YOLO关键点检测数据集配置文件
# 手部关键点检测配置

# 数据集路径配置
path: E:\xuewei\yolo_dataset  # 数据集根目录路径
train: images/train           # 训练图像相对路径
val: images/val              # 验证图像相对路径
test:                        # 测试集路径（可选，如果没有可以留空）

# 类别配置
nc: 1                        # 类别数量
names: ['hand']              # 类别名称列表

# 关键点配置
kpt_shape: [7, 3]           # [关键点数量, 坐标维度] (x, y, visibility)
flip_idx: [0, 1, 2, 3, 4, 5, 6]  # 水平翻转时关键点的对应索引

# 关键点名称定义
kpt_names:
  - zhongchong    # 0: 中冲穴
  - shaoze        # 1: 少泽穴
  - qiangu        # 2: 前谷穴
  - sifeng        # 3: 四缝穴
  - laogong       # 4: 劳宫穴
  - shaofu        # 5: 少府穴
  - wangu         # 6: 腕骨穴

# 关键点连接关系（用于可视化）
# 定义关键点之间的连接线，用于绘制骨架
#skeleton:
#  - [0, 3]        # 中冲 -> 四缝
#  - [3, 4]        # 四缝 -> 劳宫
#  - [4, 5]        # 劳宫 -> 少府
#  - [1, 2]        # 少泽 -> 前谷
#  - [2, 6]        # 前谷 -> 腕骨
#  - [5, 6]        # 少府 -> 腕骨

# 关键点颜色配置（RGB格式，用于可视化）
kpt_color:
  - [255, 0, 0]     # 中冲 - 红色
  - [0, 255, 0]     # 少泽 - 绿色
  - [0, 0, 255]     # 前谷 - 蓝色
  - [255, 255, 0]   # 四缝 - 黄色
  - [255, 0, 255]   # 劳宫 - 洋红
  - [0, 255, 255]   # 少府 - 青色
  - [255, 128, 0]   # 腕骨 - 橙色

# 骨架连接线颜色配置
skeleton_color:
  - [255, 0, 0]     # 中冲-四缝连接线
  - [0, 255, 0]     # 四缝-劳宫连接线
  - [0, 0, 255]     # 劳宫-少府连接线
  - [255, 255, 0]   # 少泽-前谷连接线
  - [255, 0, 255]   # 前谷-腕骨连接线
  - [0, 255, 255]   # 少府-腕骨连接线

# 数据增强配置（可选）
augment:
  hsv_h: 0.015      # 色调增强
  hsv_s: 0.7        # 饱和度增强
  hsv_v: 0.4        # 亮度增强
  degrees: 10.0     # 旋转角度范围
  translate: 0.1    # 平移范围
  scale: 0.9        # 缩放范围
  shear: 2.0        # 剪切变换
  perspective: 0.0  # 透视变换
  flipud: 0.0       # 垂直翻转概率
  fliplr: 0.5       # 水平翻转概率
  mixup: 0.1        # Mixup增强概率

# 训练超参数建议
hyperparameters:
  lr0: 0.01         # 初始学习率
  lrf: 0.01         # 最终学习率
  momentum: 0.937   # 动量
  weight_decay: 0.0005  # 权重衰减
  warmup_epochs: 3  # 预热轮数
  warmup_momentum: 0.8  # 预热动量
  box: 7.5          # 边界框损失权重
  cls: 0.5          # 分类损失权重
  kobj: 1.0         # 关键点目标损失权重
  
# 模型配置建议
model_config:
  input_size: 640   # 输入图像尺寸
  batch_size: 16    # 批次大小
  epochs: 100       # 训练轮数
  patience: 20      # 早停耐心值
  save_period: 10   # 模型保存周期

# 评估指标
metrics:
  - 'mAP@0.5'      # 检测精度
  - 'mAP@0.5:0.95' # 检测精度范围
  - 'OKS@0.5'      # 关键点相似度
  - 'OKS@0.5:0.95' # 关键点相似度范围

# 备注信息
description: |
  手部关键点检测数据集配置
  包含7个中医穴位关键点的检测和定位
  适用于中医按摩、针灸等应用场景
  
dataset_info:
  total_images: null        # 总图像数量（训练时自动填充）
  total_annotations: null   # 总标注数量（训练时自动填充）
  created_date: "2025-06-26"
  version: "1.0"
  author: "xuewei"